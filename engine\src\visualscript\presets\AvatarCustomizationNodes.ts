/**
 * 虚拟化身定制节点
 * 提供虚拟化身创建、定制和控制的可视化节点
 */
import { Node } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { SocketType } from '../values/ValueTypeRegistry';
import type { ExecutionContext } from '../execution/ExecutionContext';
import type { 
  AvatarCustomizationSystem,
  AvatarData,
  BodyParameters,
  ClothingItem,
  FaceParameters
} from '../../avatar/AvatarCustomizationSystem';

/**
 * 创建虚拟化身节点
 */
export class CreateAvatarNode extends Node {
  public static readonly TYPE = 'CreateAvatar';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('userId', SocketType.STRING, '', '用户ID');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');
    this.addOutputSocket('avatarData', SocketType.OBJECT, null, '虚拟化身数据');
  }

  protected async executeImpl(): Promise<any> {
    const userId = this.getInputValue('userId') as string;

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 创建虚拟化身
      const avatarData = await avatarSystem.createAvatar(userId);

      // 设置输出值
      this.setOutputValue('avatarId', avatarData.id);
      this.setOutputValue('avatarData', avatarData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return avatarData;
    } catch (error) {
      console.error('创建虚拟化身失败:', error);
      throw error;
    }
  }
}

/**
 * 从照片重建面部节点
 */
export class ReconstructFaceFromPhotoNode extends Node {
  public static readonly TYPE = 'ReconstructFaceFromPhoto';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');
    this.addInputSocket('photoData', SocketType.OBJECT, null, '照片数据');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('faceData', SocketType.OBJECT, null, '面部数据');
  }

  protected async executeImpl(): Promise<any> {
    const avatarId = this.getInputValue('avatarId') as string;
    const photoData = this.getInputValue('photoData') as ImageData;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    if (!photoData) {
      this.triggerOutput('onError');
      throw new Error('照片数据不能为空');
    }

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 重建面部
      const faceData = await avatarSystem.reconstructFaceFromPhoto(avatarId, photoData);

      // 设置输出值
      this.setOutputValue('faceData', faceData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return faceData;
    } catch (error) {
      console.error('面部重建失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 生成身体模型节点
 */
export class GenerateBodyNode extends Node {
  public static readonly TYPE = 'GenerateBody';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');
    this.addInputSocket('gender', SocketType.STRING, 'male', '性别');
    this.addInputSocket('height', SocketType.NUMBER, 170, '身高(cm)');
    this.addInputSocket('weight', SocketType.NUMBER, 70, '体重(kg)');
    this.addInputSocket('build', SocketType.NUMBER, 0, '体型(-2到2)');
    this.addInputSocket('muscle', SocketType.NUMBER, 0.5, '肌肉量(0到1)');
    this.addInputSocket('skinTone', SocketType.NUMBER, 0.5, '肤色(0到1)');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('bodyData', SocketType.OBJECT, null, '身体数据');
  }

  protected async executeImpl(): Promise<any> {
    const avatarId = this.getInputValue('avatarId') as string;
    
    const bodyParams: BodyParameters = {
      gender: this.getInputValue('gender') as 'male' | 'female',
      height: this.getInputValue('height') as number,
      weight: this.getInputValue('weight') as number,
      build: this.getInputValue('build') as number,
      muscle: this.getInputValue('muscle') as number,
      skinTone: this.getInputValue('skinTone') as number
    };

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 生成身体模型
      const bodyData = await avatarSystem.generateBody(avatarId, bodyParams);

      // 设置输出值
      this.setOutputValue('bodyData', bodyData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return bodyData;
    } catch (error) {
      console.error('身体模型生成失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 应用服装节点
 */
export class ApplyClothingNode extends Node {
  public static readonly TYPE = 'ApplyClothing';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');
    this.addInputSocket('clothingItems', SocketType.ARRAY, [], '服装列表');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('clothingData', SocketType.OBJECT, null, '服装数据');
  }

  protected async executeImpl(): Promise<any> {
    const avatarId = this.getInputValue('avatarId') as string;
    const clothingItems = this.getInputValue('clothingItems') as ClothingItem[];

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    if (!clothingItems || clothingItems.length === 0) {
      this.triggerOutput('onError');
      throw new Error('服装列表不能为空');
    }

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 应用服装
      const clothingData = await avatarSystem.applyClothing(avatarId, clothingItems);

      // 设置输出值
      this.setOutputValue('clothingData', clothingData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return clothingData;
    } catch (error) {
      console.error('服装应用失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 生成纹理节点
 */
export class GenerateTexturesNode extends Node {
  public static readonly TYPE = 'GenerateTextures';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('textureData', SocketType.OBJECT, null, '纹理数据');
  }

  protected async executeImpl(): Promise<any> {
    const avatarId = this.getInputValue('avatarId') as string;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 生成纹理
      const textureData = await avatarSystem.generateTextures(avatarId);

      // 设置输出值
      this.setOutputValue('textureData', textureData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return textureData;
    } catch (error) {
      console.error('纹理生成失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取虚拟化身数据节点
 */
export class GetAvatarDataNode extends Node {
  public static readonly TYPE = 'GetAvatarData';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onNotFound', SocketType.FLOW, null, '未找到');
    this.addOutputSocket('avatarData', SocketType.OBJECT, null, '虚拟化身数据');
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;

    if (!avatarId) {
      this.triggerOutput('onNotFound');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 获取虚拟化身数据
      const avatarData = avatarSystem.getAvatarData(avatarId);

      if (!avatarData) {
        this.triggerOutput('onNotFound');
        return null;
      }

      // 设置输出值
      this.setOutputValue('avatarData', avatarData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return avatarData;
    } catch (error) {
      console.error('获取虚拟化身数据失败:', error);
      this.triggerOutput('onNotFound');
      throw error;
    }
  }
}

/**
 * 删除虚拟化身节点
 */
export class DeleteAvatarNode extends Node {
  public static readonly TYPE = 'DeleteAvatar';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarId', SocketType.STRING, '', '虚拟化身ID');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '是否成功');
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取虚拟化身定制系统
      const avatarSystem = this.context?.world?.getSystem('AvatarCustomizationSystem') as AvatarCustomizationSystem;
      if (!avatarSystem) {
        throw new Error('虚拟化身定制系统未找到');
      }

      // 删除虚拟化身
      const success = avatarSystem.deleteAvatar(avatarId);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('删除虚拟化身失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 注册虚拟化身定制节点
 */
export function registerAvatarCustomizationNodes(registry: NodeRegistry): void {
  registry.registerNode(CreateAvatarNode.TYPE, CreateAvatarNode, {
    category: '虚拟化身',
    displayName: '创建虚拟化身',
    description: '创建一个新的虚拟化身',
    icon: 'user-plus'
  });

  registry.registerNode(ReconstructFaceFromPhotoNode.TYPE, ReconstructFaceFromPhotoNode, {
    category: '虚拟化身',
    displayName: '从照片重建面部',
    description: '从用户照片重建3D面部模型',
    icon: 'camera'
  });

  registry.registerNode(GenerateBodyNode.TYPE, GenerateBodyNode, {
    category: '虚拟化身',
    displayName: '生成身体模型',
    description: '根据参数生成身体模型',
    icon: 'user'
  });

  registry.registerNode(ApplyClothingNode.TYPE, ApplyClothingNode, {
    category: '虚拟化身',
    displayName: '应用服装',
    description: '为虚拟化身应用服装',
    icon: 'shirt'
  });

  registry.registerNode(GenerateTexturesNode.TYPE, GenerateTexturesNode, {
    category: '虚拟化身',
    displayName: '生成纹理',
    description: '为虚拟化身生成高质量纹理',
    icon: 'palette'
  });

  registry.registerNode(GetAvatarDataNode.TYPE, GetAvatarDataNode, {
    category: '虚拟化身',
    displayName: '获取虚拟化身数据',
    description: '获取虚拟化身的完整数据',
    icon: 'database'
  });

  registry.registerNode(DeleteAvatarNode.TYPE, DeleteAvatarNode, {
    category: '虚拟化身',
    displayName: '删除虚拟化身',
    description: '删除指定的虚拟化身',
    icon: 'trash'
  });
}
