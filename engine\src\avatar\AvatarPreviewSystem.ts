/**
 * 虚拟化身预览系统
 * 负责实时预览虚拟化身的变化
 */
import { EventEmitter } from '../utils/EventEmitter';
import type { AvatarData } from './AvatarCustomizationSystem';

/**
 * 预览系统配置接口
 */
export interface AvatarPreviewConfig {
  /** 调试模式 */
  debug?: boolean;
  /** 预览质量 */
  previewQuality?: 'low' | 'medium' | 'high';
  /** 是否启用实时更新 */
  enableRealTimeUpdate?: boolean;
  /** 更新频率 (ms) */
  updateFrequency?: number;
}

/**
 * 预览场景配置接口
 */
export interface PreviewSceneConfig {
  /** 背景颜色 */
  backgroundColor?: string;
  /** 光照设置 */
  lighting?: LightingConfig;
  /** 相机设置 */
  camera?: CameraConfig;
  /** 环境设置 */
  environment?: EnvironmentConfig;
}

/**
 * 光照配置接口
 */
export interface LightingConfig {
  /** 主光源强度 */
  mainLightIntensity?: number;
  /** 主光源颜色 */
  mainLightColor?: string;
  /** 环境光强度 */
  ambientLightIntensity?: number;
  /** 环境光颜色 */
  ambientLightColor?: string;
  /** 是否启用阴影 */
  enableShadows?: boolean;
}

/**
 * 相机配置接口
 */
export interface CameraConfig {
  /** 视野角度 */
  fov?: number;
  /** 相机位置 */
  position?: THREE.Vector3;
  /** 目标位置 */
  target?: THREE.Vector3;
  /** 是否启用轨道控制 */
  enableOrbitControls?: boolean;
}

/**
 * 环境配置接口
 */
export interface EnvironmentConfig {
  /** 环境贴图 */
  environmentMap?: string;
  /** 地面材质 */
  groundMaterial?: string;
  /** 是否显示网格 */
  showGrid?: boolean;
}

/**
 * 预览状态接口
 */
export interface PreviewState {
  /** 是否正在渲染 */
  isRendering: boolean;
  /** 当前FPS */
  currentFPS: number;
  /** 渲染统计 */
  renderStats: RenderStats;
  /** 最后更新时间 */
  lastUpdateTime: number;
}

/**
 * 渲染统计接口
 */
export interface RenderStats {
  /** 三角形数量 */
  triangles: number;
  /** 顶点数量 */
  vertices: number;
  /** 绘制调用次数 */
  drawCalls: number;
  /** 纹理数量 */
  textures: number;
}

/**
 * 虚拟化身预览系统
 */
export class AvatarPreviewSystem extends EventEmitter {
  /** 系统配置 */
  private config: AvatarPreviewConfig;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** Three.js 渲染器 */
  private renderer: THREE.WebGLRenderer;

  /** 预览场景 */
  private scene: THREE.Scene;

  /** 预览相机 */
  private camera: THREE.PerspectiveCamera;

  /** 轨道控制器 */
  private controls: any;

  /** 当前预览的虚拟化身 */
  private currentAvatar: THREE.Group | null = null;

  /** 预览状态 */
  private previewState: PreviewState;

  /** 更新定时器 */
  private updateTimer: number | null = null;

  /** FPS计算相关 */
  private frameCount: number = 0;
  private lastFPSUpdate: number = 0;

  /** 预览画布 */
  private canvas: HTMLCanvasElement | null = null;

  /**
   * 构造函数
   */
  constructor(config: AvatarPreviewConfig = {}) {
    super();
    
    this.config = {
      debug: false,
      previewQuality: 'medium',
      enableRealTimeUpdate: true,
      updateFrequency: 16, // 60 FPS
      ...config
    };

    this.previewState = {
      isRendering: false,
      currentFPS: 0,
      renderStats: {
        triangles: 0,
        vertices: 0,
        drawCalls: 0,
        textures: 0
      },
      lastUpdateTime: 0
    };
  }

  /**
   * 初始化系统
   */
  public async initialize(canvas?: HTMLCanvasElement): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 创建或使用提供的画布
      this.canvas = canvas || this.createCanvas();

      // 初始化Three.js组件
      this.initializeRenderer();
      this.initializeScene();
      this.initializeCamera();
      this.initializeLighting();
      this.initializeControls();

      // 开始渲染循环
      this.startRenderLoop();

      this.initialized = true;

      if (this.config.debug) {
        console.log('虚拟化身预览系统已初始化');
      }

      this.emit('initialized');
    } catch (error) {
      console.error('虚拟化身预览系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置预览虚拟化身
   */
  public setAvatar(avatarData: AvatarData): void {
    if (!this.initialized) {
      throw new Error('预览系统未初始化');
    }

    // 移除当前虚拟化身
    if (this.currentAvatar) {
      this.scene.remove(this.currentAvatar);
      this.disposeAvatarResources(this.currentAvatar);
    }

    // 创建新的虚拟化身
    this.currentAvatar = this.createAvatarMesh(avatarData);
    this.scene.add(this.currentAvatar);

    // 调整相机位置
    this.adjustCameraForAvatar();

    this.emit('avatarSet', avatarData);

    if (this.config.debug) {
      console.log('预览虚拟化身已设置', avatarData.id);
    }
  }

  /**
   * 更新虚拟化身参数
   */
  public updateAvatarParameter(category: string, parameter: string, value: any): void {
    if (!this.currentAvatar) {
      return;
    }

    // 根据参数类型更新虚拟化身
    switch (category) {
      case 'face':
        this.updateFaceParameter(parameter, value);
        break;
      case 'body':
        this.updateBodyParameter(parameter, value);
        break;
      case 'clothing':
        this.updateClothingParameter(parameter, value);
        break;
      default:
        console.warn(`未知的参数类别: ${category}`);
    }

    this.emit('parameterUpdated', { category, parameter, value });
  }

  /**
   * 创建画布
   */
  private createCanvas(): HTMLCanvasElement {
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    return canvas;
  }

  /**
   * 初始化渲染器
   */
  private initializeRenderer(): void {
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas!,
      antialias: this.config.previewQuality !== 'low',
      alpha: true
    });

    this.renderer.setSize(this.canvas!.width, this.canvas!.height);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.outputEncoding = THREE.sRGBEncoding;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xf0f0f0);
    
    // 添加网格地面
    const gridHelper = new THREE.GridHelper(10, 10);
    gridHelper.position.y = -1;
    this.scene.add(gridHelper);
  }

  /**
   * 初始化相机
   */
  private initializeCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      50,
      this.canvas!.width / this.canvas!.height,
      0.1,
      1000
    );

    this.camera.position.set(0, 1.6, 3);
    this.camera.lookAt(0, 1, 0);
  }

  /**
   * 初始化光照
   */
  private initializeLighting(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);

    // 补光
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(-5, 5, -5);
    this.scene.add(fillLight);
  }

  /**
   * 初始化控制器
   */
  private initializeControls(): void {
    // 简化的轨道控制器实现
    this.controls = {
      enabled: true,
      target: new THREE.Vector3(0, 1, 0),
      update: () => {
        // 控制器更新逻辑
      }
    };
  }

  /**
   * 创建虚拟化身网格
   */
  private createAvatarMesh(avatarData: AvatarData): THREE.Group {
    const avatarGroup = new THREE.Group();

    // 添加面部
    if (avatarData.faceData) {
      const faceMesh = new THREE.Mesh(
        avatarData.faceData.geometry,
        new THREE.MeshStandardMaterial({
          map: avatarData.faceData.texture
        })
      );
      faceMesh.position.y = 1.6;
      avatarGroup.add(faceMesh);
    }

    // 添加身体
    if (avatarData.bodyData) {
      const bodyMesh = new THREE.Mesh(
        avatarData.bodyData.geometry,
        new THREE.MeshStandardMaterial({
          map: avatarData.bodyData.texture
        })
      );
      avatarGroup.add(bodyMesh);
    }

    // 添加服装
    if (avatarData.clothingData) {
      for (const clothingItem of avatarData.clothingData.items) {
        const clothingMesh = new THREE.Mesh(
          clothingItem.geometry,
          clothingItem.material
        );
        avatarGroup.add(clothingMesh);
      }
    }

    return avatarGroup;
  }

  /**
   * 更新面部参数
   */
  private updateFaceParameter(parameter: string, value: any): void {
    // 面部参数更新逻辑
    if (this.config.debug) {
      console.log(`更新面部参数: ${parameter} = ${value}`);
    }
  }

  /**
   * 更新身体参数
   */
  private updateBodyParameter(parameter: string, value: any): void {
    // 身体参数更新逻辑
    if (this.config.debug) {
      console.log(`更新身体参数: ${parameter} = ${value}`);
    }
  }

  /**
   * 更新服装参数
   */
  private updateClothingParameter(parameter: string, value: any): void {
    // 服装参数更新逻辑
    if (this.config.debug) {
      console.log(`更新服装参数: ${parameter} = ${value}`);
    }
  }

  /**
   * 调整相机位置
   */
  private adjustCameraForAvatar(): void {
    if (!this.currentAvatar) {
      return;
    }

    // 计算虚拟化身的边界框
    const box = new THREE.Box3().setFromObject(this.currentAvatar);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());

    // 调整相机位置以适应虚拟化身
    const maxDim = Math.max(size.x, size.y, size.z);
    const distance = maxDim * 2;

    this.camera.position.set(distance, center.y + maxDim * 0.5, distance);
    this.camera.lookAt(center);

    if (this.controls) {
      this.controls.target.copy(center);
    }
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      if (!this.initialized) {
        return;
      }

      requestAnimationFrame(animate);

      // 更新控制器
      if (this.controls) {
        this.controls.update();
      }

      // 渲染场景
      this.renderer.render(this.scene, this.camera);

      // 更新FPS
      this.updateFPS();

      // 更新渲染统计
      this.updateRenderStats();
    };

    animate();
    this.previewState.isRendering = true;
  }

  /**
   * 更新FPS
   */
  private updateFPS(): void {
    this.frameCount++;
    const now = performance.now();

    if (now - this.lastFPSUpdate >= 1000) {
      this.previewState.currentFPS = this.frameCount;
      this.frameCount = 0;
      this.lastFPSUpdate = now;
    }
  }

  /**
   * 更新渲染统计
   */
  private updateRenderStats(): void {
    const info = this.renderer.info;
    
    this.previewState.renderStats = {
      triangles: info.render.triangles,
      vertices: info.render.vertices || 0,
      drawCalls: info.render.calls,
      textures: info.memory.textures
    };

    this.previewState.lastUpdateTime = performance.now();
  }

  /**
   * 获取预览状态
   */
  public getPreviewState(): PreviewState {
    return { ...this.previewState };
  }

  /**
   * 获取预览画布
   */
  public getCanvas(): HTMLCanvasElement | null {
    return this.canvas;
  }

  /**
   * 调整画布大小
   */
  public resize(width: number, height: number): void {
    if (!this.canvas || !this.renderer || !this.camera) {
      return;
    }

    this.canvas.width = width;
    this.canvas.height = height;

    this.renderer.setSize(width, height);
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
  }

  /**
   * 释放虚拟化身资源
   */
  private disposeAvatarResources(avatar: THREE.Group): void {
    avatar.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) {
          child.geometry.dispose();
        }
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
  }

  /**
   * 更新系统
   */
  public update(deltaTime: number): void {
    // 系统更新逻辑
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }

    if (this.currentAvatar) {
      this.disposeAvatarResources(this.currentAvatar);
    }

    if (this.renderer) {
      this.renderer.dispose();
    }

    this.previewState.isRendering = false;
    this.initialized = false;
    this.removeAllListeners();
  }
}
