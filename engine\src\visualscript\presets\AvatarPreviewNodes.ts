/**
 * 虚拟化身预览节点
 * 提供虚拟化身预览和控制的可视化节点
 */
import { Node } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { SocketType } from '../values/ValueTypeRegistry';
import type { ExecutionContext } from '../execution/ExecutionContext';
import type { 
  AvatarPreviewSystem,
  PreviewSceneConfig,
  PreviewState
} from '../../avatar/AvatarPreviewSystem';
import type { AvatarData } from '../../avatar/AvatarCustomizationSystem';

/**
 * 初始化预览系统节点
 */
export class InitializePreviewSystemNode extends Node {
  public static readonly TYPE = 'InitializePreviewSystem';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('canvas', SocketType.OBJECT, null, '画布元素');
    this.addInputSocket('width', SocketType.NUMBER, 800, '宽度');
    this.addInputSocket('height', SocketType.NUMBER, 600, '高度');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('previewSystem', SocketType.OBJECT, null, '预览系统');
  }

  protected async executeImpl(): Promise<any> {
    const canvas = this.getInputValue('canvas') as HTMLCanvasElement;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;

    try {
      // 获取预览系统
      let previewSystem = this.context?.world?.getSystem('AvatarPreviewSystem') as AvatarPreviewSystem;
      
      if (!previewSystem) {
        // 如果系统不存在，创建一个新的
        const { AvatarPreviewSystem } = await import('../../avatar/AvatarPreviewSystem');
        previewSystem = new AvatarPreviewSystem({
          debug: true,
          previewQuality: 'medium',
          enableRealTimeUpdate: true
        });
        
        // 添加到世界系统中
        this.context?.world?.addSystem(previewSystem);
      }

      // 初始化预览系统
      await previewSystem.initialize(canvas);

      // 调整画布大小
      if (width && height) {
        previewSystem.resize(width, height);
      }

      // 设置输出值
      this.setOutputValue('previewSystem', previewSystem);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return previewSystem;
    } catch (error) {
      console.error('初始化预览系统失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 设置预览虚拟化身节点
 */
export class SetPreviewAvatarNode extends Node {
  public static readonly TYPE = 'SetPreviewAvatar';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('avatarData', SocketType.OBJECT, null, '虚拟化身数据');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
  }

  protected executeImpl(): any {
    const avatarData = this.getInputValue('avatarData') as AvatarData;

    if (!avatarData) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身数据不能为空');
    }

    try {
      // 获取预览系统
      const previewSystem = this.context?.world?.getSystem('AvatarPreviewSystem') as AvatarPreviewSystem;
      if (!previewSystem) {
        throw new Error('预览系统未找到');
      }

      // 设置预览虚拟化身
      previewSystem.setAvatar(avatarData);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return true;
    } catch (error) {
      console.error('设置预览虚拟化身失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 更新虚拟化身参数节点
 */
export class UpdateAvatarParameterNode extends Node {
  public static readonly TYPE = 'UpdateAvatarParameter';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('category', SocketType.STRING, '', '参数类别');
    this.addInputSocket('parameter', SocketType.STRING, '', '参数名称');
    this.addInputSocket('value', SocketType.ANY, null, '参数值');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
  }

  protected executeImpl(): any {
    const category = this.getInputValue('category') as string;
    const parameter = this.getInputValue('parameter') as string;
    const value = this.getInputValue('value');

    if (!category || !parameter) {
      this.triggerOutput('onError');
      throw new Error('参数类别和参数名称不能为空');
    }

    try {
      // 获取预览系统
      const previewSystem = this.context?.world?.getSystem('AvatarPreviewSystem') as AvatarPreviewSystem;
      if (!previewSystem) {
        throw new Error('预览系统未找到');
      }

      // 更新虚拟化身参数
      previewSystem.updateAvatarParameter(category, parameter, value);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return true;
    } catch (error) {
      console.error('更新虚拟化身参数失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取预览状态节点
 */
export class GetPreviewStateNode extends Node {
  public static readonly TYPE = 'GetPreviewState';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('previewState', SocketType.OBJECT, null, '预览状态');
    this.addOutputSocket('isRendering', SocketType.BOOLEAN, false, '是否正在渲染');
    this.addOutputSocket('currentFPS', SocketType.NUMBER, 0, '当前FPS');
    this.addOutputSocket('triangles', SocketType.NUMBER, 0, '三角形数量');
    this.addOutputSocket('vertices', SocketType.NUMBER, 0, '顶点数量');
  }

  protected executeImpl(): any {
    try {
      // 获取预览系统
      const previewSystem = this.context?.world?.getSystem('AvatarPreviewSystem') as AvatarPreviewSystem;
      if (!previewSystem) {
        throw new Error('预览系统未找到');
      }

      // 获取预览状态
      const previewState = previewSystem.getPreviewState();

      // 设置输出值
      this.setOutputValue('previewState', previewState);
      this.setOutputValue('isRendering', previewState.isRendering);
      this.setOutputValue('currentFPS', previewState.currentFPS);
      this.setOutputValue('triangles', previewState.renderStats.triangles);
      this.setOutputValue('vertices', previewState.renderStats.vertices);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return previewState;
    } catch (error) {
      console.error('获取预览状态失败:', error);
      throw error;
    }
  }
}

/**
 * 调整预览画布大小节点
 */
export class ResizePreviewCanvasNode extends Node {
  public static readonly TYPE = 'ResizePreviewCanvas';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('width', SocketType.NUMBER, 800, '宽度');
    this.addInputSocket('height', SocketType.NUMBER, 600, '高度');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
  }

  protected executeImpl(): any {
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;

    if (width <= 0 || height <= 0) {
      this.triggerOutput('onError');
      throw new Error('宽度和高度必须大于0');
    }

    try {
      // 获取预览系统
      const previewSystem = this.context?.world?.getSystem('AvatarPreviewSystem') as AvatarPreviewSystem;
      if (!previewSystem) {
        throw new Error('预览系统未找到');
      }

      // 调整画布大小
      previewSystem.resize(width, height);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return true;
    } catch (error) {
      console.error('调整预览画布大小失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取预览画布节点
 */
export class GetPreviewCanvasNode extends Node {
  public static readonly TYPE = 'GetPreviewCanvas';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('onError', SocketType.FLOW, null, '错误');
    this.addOutputSocket('canvas', SocketType.OBJECT, null, '画布元素');
  }

  protected executeImpl(): any {
    try {
      // 获取预览系统
      const previewSystem = this.context?.world?.getSystem('AvatarPreviewSystem') as AvatarPreviewSystem;
      if (!previewSystem) {
        throw new Error('预览系统未找到');
      }

      // 获取预览画布
      const canvas = previewSystem.getCanvas();

      if (!canvas) {
        this.triggerOutput('onError');
        throw new Error('预览画布未初始化');
      }

      // 设置输出值
      this.setOutputValue('canvas', canvas);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return canvas;
    } catch (error) {
      console.error('获取预览画布失败:', error);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 创建身体参数对象节点
 */
export class CreateBodyParametersNode extends Node {
  public static readonly TYPE = 'CreateBodyParameters';

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('trigger', SocketType.FLOW, null, '触发');
    this.addInputSocket('gender', SocketType.STRING, 'male', '性别');
    this.addInputSocket('height', SocketType.NUMBER, 170, '身高(cm)');
    this.addInputSocket('weight', SocketType.NUMBER, 70, '体重(kg)');
    this.addInputSocket('build', SocketType.NUMBER, 0, '体型(-2到2)');
    this.addInputSocket('muscle', SocketType.NUMBER, 0.5, '肌肉量(0到1)');
    this.addInputSocket('skinTone', SocketType.NUMBER, 0.5, '肤色(0到1)');

    // 输出插槽
    this.addOutputSocket('onComplete', SocketType.FLOW, null, '完成');
    this.addOutputSocket('bodyParameters', SocketType.OBJECT, null, '身体参数');
  }

  protected executeImpl(): any {
    const bodyParameters = {
      gender: this.getInputValue('gender') as 'male' | 'female',
      height: this.getInputValue('height') as number,
      weight: this.getInputValue('weight') as number,
      build: this.getInputValue('build') as number,
      muscle: this.getInputValue('muscle') as number,
      skinTone: this.getInputValue('skinTone') as number
    };

    // 验证参数
    if (!['male', 'female'].includes(bodyParameters.gender)) {
      throw new Error('性别必须是 male 或 female');
    }

    if (bodyParameters.height < 100 || bodyParameters.height > 250) {
      throw new Error('身高必须在100-250cm之间');
    }

    if (bodyParameters.weight < 30 || bodyParameters.weight > 200) {
      throw new Error('体重必须在30-200kg之间');
    }

    if (bodyParameters.build < -2 || bodyParameters.build > 2) {
      throw new Error('体型参数必须在-2到2之间');
    }

    if (bodyParameters.muscle < 0 || bodyParameters.muscle > 1) {
      throw new Error('肌肉量必须在0到1之间');
    }

    if (bodyParameters.skinTone < 0 || bodyParameters.skinTone > 1) {
      throw new Error('肤色参数必须在0到1之间');
    }

    // 设置输出值
    this.setOutputValue('bodyParameters', bodyParameters);

    // 触发完成流程
    this.triggerOutput('onComplete');

    return bodyParameters;
  }
}

/**
 * 注册虚拟化身预览节点
 */
export function registerAvatarPreviewNodes(registry: NodeRegistry): void {
  registry.registerNode(InitializePreviewSystemNode.TYPE, InitializePreviewSystemNode, {
    category: '虚拟化身预览',
    displayName: '初始化预览系统',
    description: '初始化虚拟化身预览系统',
    icon: 'monitor'
  });

  registry.registerNode(SetPreviewAvatarNode.TYPE, SetPreviewAvatarNode, {
    category: '虚拟化身预览',
    displayName: '设置预览虚拟化身',
    description: '在预览系统中设置要显示的虚拟化身',
    icon: 'eye'
  });

  registry.registerNode(UpdateAvatarParameterNode.TYPE, UpdateAvatarParameterNode, {
    category: '虚拟化身预览',
    displayName: '更新虚拟化身参数',
    description: '实时更新虚拟化身的参数',
    icon: 'sliders'
  });

  registry.registerNode(GetPreviewStateNode.TYPE, GetPreviewStateNode, {
    category: '虚拟化身预览',
    displayName: '获取预览状态',
    description: '获取预览系统的当前状态',
    icon: 'info'
  });

  registry.registerNode(ResizePreviewCanvasNode.TYPE, ResizePreviewCanvasNode, {
    category: '虚拟化身预览',
    displayName: '调整预览画布大小',
    description: '调整预览画布的尺寸',
    icon: 'resize'
  });

  registry.registerNode(GetPreviewCanvasNode.TYPE, GetPreviewCanvasNode, {
    category: '虚拟化身预览',
    displayName: '获取预览画布',
    description: '获取预览系统的画布元素',
    icon: 'image'
  });

  registry.registerNode(CreateBodyParametersNode.TYPE, CreateBodyParametersNode, {
    category: '虚拟化身预览',
    displayName: '创建身体参数',
    description: '创建身体参数对象',
    icon: 'settings'
  });
}
